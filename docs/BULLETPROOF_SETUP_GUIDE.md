# 🛡️ Bulletproof Setup Guide

This guide ensures a 100% reliable setup experience for Kitco Research AI on any platform, preventing all the common issues that developers encounter.

## 🎯 What We Fixed

Based on real-world setup issues, we've created a bulletproof setup system that handles:

### ❌ **Common Issues We Prevent**
- Missing dependencies and package conflicts
- Database initialization timing problems
- Port conflicts and process cleanup
- Environment file creation failures
- Virtual environment corruption
- Platform-specific path issues
- Network connectivity problems
- Permission and antivirus interference
- Incomplete installations

### ✅ **Our Solutions**
- **Robust error handling** with retry logic
- **Pre-flight system checks** before setup
- **Dependency validation** and automatic fixing
- **Cross-platform compatibility** (Windows, Linux, macOS)
- **Comprehensive logging** for troubleshooting
- **Automatic cleanup** and recovery
- **Multiple setup methods** for different scenarios

## 🚀 Quick Start (Recommended)

### **Step 1: Pre-flight Check**
Run this first to validate your system:

```bash
# All platforms
python scripts/preflight_check.py
```

### **Step 2: Robust Setup**

#### **Windows** 🪟
```cmd
# Bulletproof setup
scripts\setup.bat --development

# Or use cross-platform utility
python scripts/setup_cross_platform.py setup --robust --development
```

#### **Linux/macOS** 🐧🍎
```bash
# Bulletproof setup
./scripts/setup_robust.sh --development

# Or use cross-platform utility
python scripts/setup_cross_platform.py setup --robust --development
```

### **Step 3: Validation**
```bash
# Validate everything is working
python scripts/validate_requirements.py
```

### **Step 4: Start Application**
```bash
# Windows
scripts\start_app.bat

# Linux/macOS
./scripts/start_app.sh

# Cross-platform
python scripts/setup_cross_platform.py start
```

## 🔧 Setup Methods

### **Method 1: Robust Scripts** (Recommended)
Enhanced scripts with comprehensive error handling:

**Windows:**
```cmd
scripts\setup.bat --development
```

**Linux/macOS:**
```bash
./scripts/setup_robust.sh --development
```

### **Method 2: Cross-Platform Utility**
Python-based utility that works everywhere:

```bash
# Pre-flight check
python scripts/setup_cross_platform.py preflight

# Robust setup
python scripts/setup_cross_platform.py setup --robust --development

# Validation
python scripts/setup_cross_platform.py validate

# Start application
python scripts/setup_cross_platform.py start
```

### **Method 3: Step-by-Step Manual**
For maximum control and troubleshooting:

```bash
# 1. Pre-flight check
python scripts/preflight_check.py

# 2. Requirements validation
python scripts/validate_requirements.py

# 3. Traditional setup (if needed)
./scripts/setup.sh --development  # Linux/macOS
scripts\setup.bat --development   # Windows
```

## 🛠️ Available Tools

### **Pre-flight Check** (`scripts/preflight_check.py`)
Validates your system before setup:
- ✅ Python version and installation
- ✅ Network connectivity
- ✅ Disk space availability
- ✅ Required system commands
- ✅ Port availability
- ✅ File system permissions
- ✅ Project structure
- ✅ Antivirus interference detection

### **Requirements Validator** (`scripts/validate_requirements.py`)
Fixes common dependency issues:
- ✅ Checks critical packages
- ✅ Installs missing dependencies
- ✅ Validates Playwright browsers
- ✅ Verifies database setup
- ✅ Creates environment files

### **Robust Setup Scripts**
Enhanced setup with error handling:
- ✅ Retry logic for failed installations
- ✅ Automatic cleanup and recovery
- ✅ Comprehensive logging
- ✅ Platform-specific optimizations
- ✅ Validation at each step

## 🔍 Troubleshooting

### **If Setup Fails**

1. **Check the logs:**
   ```bash
   # Windows
   type %TEMP%\kitco_setup.log
   
   # Linux/macOS
   cat /tmp/kitco_setup_*.log
   ```

2. **Run pre-flight check:**
   ```bash
   python scripts/preflight_check.py
   ```

3. **Try step-by-step validation:**
   ```bash
   python scripts/validate_requirements.py
   ```

4. **Clean start:**
   ```bash
   # Remove virtual environment
   rm -rf .venv  # Linux/macOS
   rmdir /s .venv  # Windows
   
   # Run robust setup
   python scripts/setup_cross_platform.py setup --robust --development
   ```

### **Common Issues and Solutions**

#### **Python Not Found**
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install python3.10 python3.10-venv python3-pip

# macOS
brew install python@3.10

# Windows
# Download from https://python.org and ensure "Add to PATH" is checked
```

#### **Permission Denied**
```bash
# Linux/macOS
sudo chown -R $USER:$USER .
chmod +x scripts/*.sh

# Windows
# Run Command Prompt as Administrator
```

#### **Network Issues**
```bash
# Check connectivity
python scripts/preflight_check.py

# Configure proxy if needed
pip config set global.proxy http://proxy.company.com:8080
```

#### **Port Already in Use**
```bash
# Clean restart
./scripts/restart.sh  # Linux/macOS
scripts\restart.bat   # Windows

# Or use different port
./scripts/start_app.sh 5000
```

#### **Antivirus Interference (Windows)**
- Add project directory to antivirus exclusions
- Temporarily disable real-time protection during setup
- Use Windows Defender exclusions for Python and project folders

## 🎯 Environment Types

### **Development** (Recommended for developers)
```bash
--development
```
- All dependencies + development tools
- Testing frameworks (pytest)
- Code formatters (black)
- Type checkers (mypy)
- Linters (pylint)
- Security scanners (pip-audit)

### **Production** (For deployment)
```bash
--production
```
- Minimal dependencies only
- No development tools
- Optimized for security
- Smaller footprint

### **Standard** (For regular usage)
```bash
--env standard
```
- All application features
- No development tools
- Good for end users

## 🔄 Maintenance Commands

### **Update Dependencies**
```bash
# Activate virtual environment first
source .venv/bin/activate  # Linux/macOS
.venv\Scripts\activate.bat  # Windows

# Update packages
pip install --upgrade -r config/requirements.txt

# Validate after update
python scripts/validate_requirements.py
```

### **Clean Restart**
```bash
# Full cleanup and restart
./scripts/restart.sh  # Linux/macOS
scripts\restart.bat   # Windows
```

### **Health Check**
```bash
# Check system health
python scripts/preflight_check.py

# Validate requirements
python scripts/validate_requirements.py
```

## 📊 Success Metrics

After following this guide, you should have:

- ✅ **100% reliable setup** on any supported platform
- ✅ **Zero dependency conflicts** or missing packages
- ✅ **Automatic error recovery** for common issues
- ✅ **Comprehensive logging** for troubleshooting
- ✅ **Cross-platform compatibility** (Windows, Linux, macOS)
- ✅ **Production-ready configuration** options
- ✅ **Developer-friendly** tools and workflows

## 🆘 Getting Help

If you still encounter issues after following this guide:

1. **Check logs** in the locations mentioned above
2. **Run all validation tools** to identify specific problems
3. **Review the troubleshooting section** for your specific issue
4. **Use the cross-platform utility** for consistent behavior
5. **Try the step-by-step manual method** for maximum control

## 🎉 Success!

Once setup is complete, you'll have a bulletproof Kitco Research AI installation that:
- Starts reliably every time
- Handles errors gracefully
- Works consistently across platforms
- Provides excellent developer experience
- Is ready for both development and production use

**Access your application at: http://localhost:8765**
