# 🌍 Cross-Platform Setup Guide

This guide provides detailed instructions for setting up Kitco Research AI on Windows, Linux, and macOS.

## 📋 Prerequisites

### **All Platforms**
- Python 3.10 or higher
- Git (for cloning the repository)
- Internet connection (for downloading dependencies)

### **Platform-Specific Requirements**

#### **Windows** 🪟
- Windows 10 or higher
- PowerShell or Command Prompt
- Python installed from [python.org](https://python.org) or Microsoft Store

#### **Linux** 🐧
- Ubuntu 20.04+ / Debian 11+ / CentOS 8+ / Fedora 35+
- Bash shell
- Python 3.10+ (install via package manager)

#### **macOS** 🍎
- macOS 11.0 (Big Sur) or higher
- Bash or Zsh shell
- Python 3.10+ (install via Homebrew or python.org)

## 🚀 Quick Setup

### **Method 1: Platform-Specific Scripts** (Recommended)

#### **Windows** 🪟
```cmd
# Clone the repository
git clone https://github.com/your-repo/kitco_research_ai.git
cd kitco_research_ai

# Run setup
setup.bat --development

# Start the application
start.bat
```

#### **Linux/macOS** 🐧🍎
```bash
# Clone the repository
git clone https://github.com/your-repo/kitco_research_ai.git
cd kitco_research_ai

# Make scripts executable (if needed)
chmod +x setup start restart

# Run setup
./setup --development

# Start the application
./start
```

### **Method 2: Cross-Platform Python Utility**

Works on all platforms:
```bash
# Clone the repository
git clone https://github.com/your-repo/kitco_research_ai.git
cd kitco_research_ai

# Check prerequisites
python scripts/setup_cross_platform.py check

# Run setup
python scripts/setup_cross_platform.py setup --development

# Start the application
python scripts/setup_cross_platform.py start
```

## 🔧 Detailed Setup Instructions

### **Step 1: Install Python**

#### **Windows** 🪟
1. Download Python from [python.org](https://python.org/downloads/)
2. Run the installer and check "Add Python to PATH"
3. Verify installation:
   ```cmd
   python --version
   pip --version
   ```

#### **Linux** 🐧
**Ubuntu/Debian:**
```bash
sudo apt update
sudo apt install python3.10 python3.10-venv python3-pip
```

**CentOS/RHEL/Fedora:**
```bash
sudo dnf install python3.10 python3-pip
```

#### **macOS** 🍎
**Using Homebrew (Recommended):**
```bash
brew install python@3.10
```

**Using python.org:**
1. Download from [python.org](https://python.org/downloads/)
2. Run the installer

### **Step 2: Clone Repository**

All platforms:
```bash
git clone https://github.com/your-repo/kitco_research_ai.git
cd kitco_research_ai
```

### **Step 3: Run Setup**

Choose your preferred method:

#### **Option A: Convenience Scripts**

**Windows:**
```cmd
setup.bat --development
```

**Linux/macOS:**
```bash
./setup --development
```

#### **Option B: Direct Script Access**

**Windows:**
```cmd
scripts\setup.bat --development
```

**Linux/macOS:**
```bash
./scripts/setup.sh --development
```

#### **Option C: Python Cross-Platform**
```bash
python scripts/setup_cross_platform.py setup --development
```

### **Step 4: Start Application**

#### **Option A: Convenience Scripts**

**Windows:**
```cmd
start.bat
```

**Linux/macOS:**
```bash
./start
```

#### **Option B: Direct Script Access**

**Windows:**
```cmd
scripts\start_app.bat
```

**Linux/macOS:**
```bash
./scripts/start_app.sh
```

#### **Option C: Python Cross-Platform**
```bash
python scripts/setup_cross_platform.py start
```

## 🎯 Environment Types

### **Development Environment** (Recommended)
- **Purpose**: Full development setup with testing and debugging tools
- **Command**: `--development`
- **Includes**: pytest, black, mypy, pylint, pip-audit

### **Production Environment**
- **Purpose**: Minimal setup for production deployment
- **Command**: `--production`
- **Includes**: Only essential runtime dependencies

### **Standard Environment**
- **Purpose**: All features without development tools
- **Command**: (default) or `--env standard`
- **Includes**: All application features, no dev tools

## 🔄 Common Operations

### **Restart Application**

**Windows:**
```cmd
restart.bat
```

**Linux/macOS:**
```bash
./restart
```

**Cross-Platform:**
```bash
python scripts/setup_cross_platform.py restart
```

### **Change Port**

**Windows:**
```cmd
start.bat 5000
```

**Linux/macOS:**
```bash
./start 5000
```

**Cross-Platform:**
```bash
python scripts/setup_cross_platform.py start --port 5000
```

### **Check Status**

**Linux/macOS only:**
```bash
./scripts/status.sh
```

**Cross-Platform:**
```bash
python scripts/setup_cross_platform.py check
```

## 🐛 Troubleshooting

### **Common Issues**

#### **Python Not Found**
- **Windows**: Ensure Python is in PATH, reinstall with "Add to PATH" checked
- **Linux**: Install python3.10 package
- **macOS**: Install via Homebrew or python.org

#### **Permission Denied (Linux/macOS)**
```bash
chmod +x setup start restart
chmod +x scripts/*.sh
```

#### **Port Already in Use**
```bash
# Use different port
./start 5000  # Linux/macOS
start.bat 5000  # Windows

# Or restart to clean up
./restart  # Linux/macOS
restart.bat  # Windows
```

#### **Virtual Environment Issues**
Delete `.venv` directory and run setup again:

**Windows:**
```cmd
rmdir /s .venv
setup.bat --development
```

**Linux/macOS:**
```bash
rm -rf .venv
./setup --development
```

### **Platform-Specific Issues**

#### **Windows**
- Use PowerShell or Command Prompt (not Git Bash for batch files)
- Ensure antivirus doesn't block Python execution
- Run as Administrator if permission issues occur

#### **Linux**
- Install development headers: `sudo apt install python3-dev build-essential`
- For older distributions, use deadsnakes PPA for Python 3.10+

#### **macOS**
- Install Xcode Command Line Tools: `xcode-select --install`
- Use Homebrew for package management

## 📚 Additional Resources

- [Main README](../README.md) - Quick start guide
- [Architecture Documentation](ARCHITECTURE.md) - Technical details
- [Production Guide](PRODUCTION.md) - Deployment instructions
- [Security Guide](SECURITY.md) - Security configuration

## 🆘 Getting Help

1. **Check Prerequisites**: Run `python scripts/setup_cross_platform.py check`
2. **Review Logs**: Check console output for specific error messages
3. **Clean Setup**: Delete `.venv` and run setup again
4. **Platform Help**: 
   - Windows: Check Windows-specific documentation
   - Linux: Use distribution package manager
   - macOS: Use Homebrew for dependencies
