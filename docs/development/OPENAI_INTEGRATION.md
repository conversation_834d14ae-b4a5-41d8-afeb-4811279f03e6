# OpenAI API Integration Guide

This document provides comprehensive guidance for integrating and using OpenAI's API with Kitco Research AI, following the latest OpenAI documentation and best practices.

## 🔑 API Key Setup

### Getting Your API Key

1. **Visit OpenAI Platform**: Go to [https://platform.openai.com/api-keys](https://platform.openai.com/api-keys)
2. **Create Account**: Sign up or log in to your OpenAI account
3. **Generate API Key**: Click "Create new secret key"
4. **Copy Key**: Save the key securely (you won't be able to see it again)

### API Key Formats

OpenAI uses two API key formats:

#### New Project-Based Format (Recommended)
- **Format**: `sk-proj-...`
- **Length**: 164 characters
- **Example**: `********************************************************************************************************************************************************************`

#### Legacy Format (Still Supported)
- **Format**: `sk-...`
- **Length**: 51 characters
- **Example**: `sk-1234567890abcdef1234567890abcdef1234567890abcdef`

## 🛠️ Configuration

### Environment Variables

Add your OpenAI API key to the `.env` file:

```bash
# OpenAI API Key - New format: sk-proj-... (164 characters)
# Get your API key from: https://platform.openai.com/api-keys
OPENAI_API_KEY=sk-proj-your-actual-api-key-here
```

### Application Configuration

The application automatically detects and validates your API key format. Configuration is handled in:

- **Main Config**: `config/app_config.json`
- **Default Config**: `config/default_config.json`
- **Environment Loading**: `config/environment.py`
- **Secrets Management**: `config/secrets_manager.py`

## 🔧 Technical Implementation

### Dependencies

The project uses the latest OpenAI packages:

```
openai==1.68.2                 # Official OpenAI Python client
langchain-openai==0.3.17       # LangChain OpenAI integration
langchain==0.3.25              # LangChain framework
```

### API Key Validation

The application includes robust API key validation:

```python
def validate_openai_api_key(api_key: str) -> bool:
    """Validate OpenAI API key format."""
    if not api_key or not isinstance(api_key, str):
        return False
    
    # New project-based format: sk-proj-... (164 characters)
    if api_key.startswith("sk-proj-") and len(api_key) == 164:
        return True
    
    # Legacy format: sk-... (51 characters)
    if api_key.startswith("sk-") and len(api_key) == 51:
        return True
    
    return False
```

### Error Handling

The application includes comprehensive error handling:

- **Missing API Key**: Falls back to local models (Ollama)
- **Invalid API Key Format**: Validates and warns about format issues
- **API Errors**: Catches and logs OpenAI API errors
- **Network Issues**: Handles connection problems gracefully

## 🎯 Available Models

### GPT Models

The application supports all current OpenAI models:

- **GPT-4o**: Latest and most capable model
- **GPT-4 Turbo**: High performance, cost-effective
- **GPT-3.5 Turbo**: Fast and economical

### Model Configuration

Configure models in the settings interface or configuration files:

```json
{
  "llm": {
    "technical_settings": {
      "provider": "openai",
      "model": "gpt-4o",
      "max_tokens": 4000,
      "temperature": 0.7
    }
  }
}
```

## 🔒 Security Best Practices

### API Key Security

1. **Never Commit Keys**: API keys are automatically excluded from version control
2. **Environment Variables**: Store keys in `.env` file only
3. **Secure Storage**: Production uses encrypted secrets management
4. **Key Rotation**: Regularly rotate API keys for security

### Access Control

- **Rate Limiting**: Built-in rate limiting prevents API abuse
- **Error Logging**: Secure logging without exposing sensitive data
- **Fallback Models**: Graceful degradation when API is unavailable

## 🚀 Usage Examples

### Basic Usage

```python
from kitco_research_ai.config.llm_config import get_llm

# Get OpenAI model
llm = get_llm(
    model_name="gpt-4o",
    provider="openai",
    temperature=0.7
)

# Use the model
response = llm.invoke("Your research question here")
```

### Advanced Configuration

```python
# Custom endpoint (for OpenRouter, etc.)
llm = get_llm(
    model_name="gpt-4o",
    provider="openai_endpoint",
    openai_endpoint_url="https://openrouter.ai/api/v1"
)
```

## 🔍 Troubleshooting

### Common Issues

#### Invalid API Key
```
WARNING: Invalid OpenAI API key format. Expected 'sk-proj-...' (164 chars) or 'sk-...' (51 chars)
```
**Solution**: Check your API key format and ensure it's correctly copied.

#### API Key Not Found
```
WARNING: OPENAI_API_KEY not found. Falling back to default model.
```
**Solution**: Add your API key to the `.env` file.

#### Rate Limiting
```
ERROR: OpenAI API rate limit exceeded
```
**Solution**: Wait and retry, or upgrade your OpenAI plan.

### Debugging

Enable debug logging to troubleshoot issues:

```bash
export LOG_LEVEL=DEBUG
./start
```

## 📊 Monitoring and Costs

### Usage Tracking

Monitor your OpenAI usage at [https://platform.openai.com/usage](https://platform.openai.com/usage)

### Cost Optimization

1. **Model Selection**: Use GPT-3.5 Turbo for simpler tasks
2. **Token Management**: Configure appropriate max_tokens limits
3. **Caching**: Enable response caching for repeated queries
4. **Fallback Models**: Use local models when possible

## 🔄 Migration from Legacy Keys

If you have an old API key format:

1. **Generate New Key**: Create a new project-based key
2. **Update Configuration**: Replace the old key in `.env`
3. **Test Integration**: Verify the new key works correctly
4. **Revoke Old Key**: Delete the old key from OpenAI dashboard

## 📚 Additional Resources

- **OpenAI Documentation**: [https://platform.openai.com/docs](https://platform.openai.com/docs)
- **API Reference**: [https://platform.openai.com/docs/api-reference](https://platform.openai.com/docs/api-reference)
- **LangChain OpenAI**: [https://python.langchain.com/docs/integrations/llms/openai](https://python.langchain.com/docs/integrations/llms/openai)
- **Rate Limits**: [https://platform.openai.com/docs/guides/rate-limits](https://platform.openai.com/docs/guides/rate-limits)

## ✅ Verification Checklist

- [ ] API key obtained from OpenAI platform
- [ ] API key added to `.env` file
- [ ] API key format validated (164 or 51 characters)
- [ ] Application starts without API key errors
- [ ] OpenAI models available in settings
- [ ] Test query successful with OpenAI model
- [ ] Fallback to local models works when API unavailable
