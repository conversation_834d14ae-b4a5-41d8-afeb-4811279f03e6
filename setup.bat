@echo off
:: Kitco Research AI - Windows Setup Convenience Script
:: This script provides easy access to the setup functionality

:: Check if we're in the right directory
if not exist "app.py" (
    echo ❌ Error: This script must be run from the project root directory
    echo    Please navigate to the kitco_research_ai directory first
    pause
    exit /b 1
)

:: Run the Windows setup script
call scripts\setup.bat %*

:: Pause to show results
if errorlevel 1 (
    echo.
    echo ❌ Setup failed. Please check the error messages above.
    pause
) else (
    echo.
    echo ✅ Setup completed successfully!
    echo 🚀 You can now run: start.bat
    pause
)
