#!/usr/bin/env python3
"""
Kitco Research AI - Cross-Platform Setup Utility
This Python script provides cross-platform setup functionality
"""

import os
import sys
import platform
import subprocess
import argparse
from pathlib import Path


def get_platform_info():
    """Get platform information"""
    system = platform.system().lower()
    return {
        'system': system,
        'is_windows': system == 'windows',
        'is_linux': system == 'linux',
        'is_macos': system == 'darwin',
        'python_executable': sys.executable,
        'shell_extension': '.bat' if system == 'windows' else '.sh'
    }


def find_project_root():
    """Find the project root directory"""
    current_dir = Path.cwd()
    
    # Check current directory first
    if (current_dir / 'app.py').exists() and (current_dir / 'src' / 'kitco_research_ai').exists():
        return current_dir
    
    # Search upward
    for parent in current_dir.parents:
        if (parent / 'app.py').exists() and (parent / 'src' / 'kitco_research_ai').exists():
            return parent
    
    raise FileNotFoundError("Could not find project root directory")


def run_platform_script(script_name, args=None, platform_info=None):
    """Run the appropriate platform-specific script"""
    if platform_info is None:
        platform_info = get_platform_info()
    
    project_root = find_project_root()
    os.chdir(project_root)
    
    # Determine script path
    script_extension = platform_info['shell_extension']
    script_path = project_root / 'scripts' / f'{script_name}{script_extension}'
    
    if not script_path.exists():
        raise FileNotFoundError(f"Script not found: {script_path}")
    
    # Prepare command
    if platform_info['is_windows']:
        cmd = [str(script_path)]
    else:
        cmd = ['bash', str(script_path)]
    
    if args:
        cmd.extend(args)
    
    print(f"🚀 Running {script_name} for {platform_info['system'].title()}...")
    print(f"📁 Project Root: {project_root}")
    print(f"🔧 Command: {' '.join(cmd)}")
    print()
    
    # Run the script
    try:
        result = subprocess.run(cmd, check=True)
        return result.returncode
    except subprocess.CalledProcessError as e:
        print(f"❌ Script failed with exit code: {e.returncode}")
        return e.returncode
    except FileNotFoundError:
        print(f"❌ Could not execute script: {script_path}")
        return 1


def check_python_version():
    """Check if Python version is compatible"""
    version = sys.version_info
    if version < (3, 10):
        print(f"❌ Python 3.10 or higher is required")
        print(f"   Current version: {version.major}.{version.minor}.{version.micro}")
        return False
    
    print(f"✅ Python version check passed: {version.major}.{version.minor}.{version.micro}")
    return True


def check_prerequisites():
    """Check system prerequisites"""
    platform_info = get_platform_info()
    
    print("🔍 Checking system prerequisites...")
    print(f"🖥️  Operating System: {platform_info['system'].title()}")
    print(f"🐍 Python Executable: {platform_info['python_executable']}")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check if required commands are available
    required_commands = ['pip']
    if platform_info['is_windows']:
        required_commands.extend(['python'])
    else:
        required_commands.extend(['python3'])
    
    for cmd in required_commands:
        try:
            subprocess.run([cmd, '--version'], 
                         capture_output=True, check=True)
            print(f"✅ {cmd} is available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {cmd} is not available or not working")
            return False
    
    print("✅ All prerequisites met")
    return True


def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(
        description='Cross-platform setup utility for Kitco Research AI'
    )
    parser.add_argument(
        'action', 
        choices=['setup', 'start', 'restart', 'check'],
        help='Action to perform'
    )
    parser.add_argument(
        '--env', 
        choices=['dev', 'prod', 'standard'],
        default='dev',
        help='Environment type (default: dev)'
    )
    parser.add_argument(
        '--development', 
        action='store_true',
        help='Use development environment'
    )
    parser.add_argument(
        '--production', 
        action='store_true',
        help='Use production environment'
    )
    parser.add_argument(
        '--port', 
        type=int,
        default=8765,
        help='Port number for start/restart (default: 8765)'
    )
    
    args = parser.parse_args()
    
    # Get platform information
    platform_info = get_platform_info()
    
    print("🔬 Kitco Research AI - Cross-Platform Setup")
    print("=" * 50)
    print(f"🖥️  Platform: {platform_info['system'].title()}")
    print()
    
    try:
        if args.action == 'check':
            # Just check prerequisites
            success = check_prerequisites()
            return 0 if success else 1
        
        # Check prerequisites for other actions
        if not check_prerequisites():
            return 1
        
        # Prepare arguments for platform scripts
        script_args = []
        
        if args.action == 'setup':
            if args.development:
                script_args.append('--development')
            elif args.production:
                script_args.append('--production')
            else:
                script_args.extend(['--env', args.env])
        
        elif args.action in ['start', 'restart']:
            if args.port != 8765:
                script_args.append(str(args.port))
        
        # Map action to script name
        script_mapping = {
            'setup': 'setup',
            'start': 'start_app',
            'restart': 'restart'
        }
        
        script_name = script_mapping[args.action]
        return run_platform_script(script_name, script_args, platform_info)
        
    except FileNotFoundError as e:
        print(f"❌ Error: {e}")
        return 1
    except KeyboardInterrupt:
        print("\n⏹️  Operation cancelled by user")
        return 1
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
