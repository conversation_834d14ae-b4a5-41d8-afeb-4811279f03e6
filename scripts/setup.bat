@echo off
:: Kitco Research AI - Robust Initial Setup Script for Windows
:: This script performs bulletproof initial setup with comprehensive error handling

setlocal enabledelayedexpansion

echo.
echo 🚀 Kitco Research AI - Robust Setup System
echo ==========================================
echo 🎯 Ensuring bulletproof setup for all developers
echo.

:: Set error handling
set "SETUP_FAILED=false"
set "SETUP_LOG=%TEMP%\kitco_setup.log"

:: Initialize log
echo Setup started at %DATE% %TIME% > "%SETUP_LOG%"

:: Find project root directory
call :find_project_root
if errorlevel 1 (
    echo ❌ FATAL: Could not find project root directory
    echo Please run this script from the kitco_research_ai project directory
    pause
    exit /b 1
)

cd /d "%PROJECT_ROOT%"
echo 📁 Project Root: %PROJECT_ROOT%
echo 📝 Setup Log: %SETUP_LOG%
echo.

:: Parse command line arguments
set "ENVIRONMENT=dev"
set "HELP=false"

:parse_args
if "%~1"=="" goto end_parse_args
if "%~1"=="--env" (
    set "ENVIRONMENT=%~2"
    shift
    shift
    goto parse_args
)
if "%~1"=="--production" (
    set "ENVIRONMENT=prod"
    shift
    goto parse_args
)
if "%~1"=="--development" (
    set "ENVIRONMENT=dev"
    shift
    goto parse_args
)
if "%~1"=="--help" (
    set "HELP=true"
    shift
    goto parse_args
)
if "%~1"=="-h" (
    set "HELP=true"
    shift
    goto parse_args
)

echo ❌ Unknown option: %~1
echo Use --help for usage information
exit /b 1

:end_parse_args

:: Show help if requested
if "%HELP%"=="true" (
    echo Usage: %0 ^[OPTIONS^]
    echo.
    echo Options:
    echo   --env ENV          Set environment ^(dev, prod, standard^) ^[default: dev^]
    echo   --development      Use development environment ^(includes dev tools^)
    echo   --production       Use production environment ^(minimal dependencies^)
    echo   --help, -h         Show this help message
    echo.
    echo Environments:
    echo   dev        Development environment with all tools ^(requirements-dev.txt^)
    echo   prod       Production environment with minimal dependencies ^(requirements-prod.txt^)
    echo   standard   Standard environment with all features ^(requirements.txt^)
    echo.
    echo Examples:
    echo   %0                    # Development setup ^(default^)
    echo   %0 --development      # Development setup ^(explicit^)
    echo   %0 --production       # Production setup
    echo   %0 --env standard     # Standard setup
    exit /b 0
)

:: Determine requirements file based on environment
echo Current environment: [%ENVIRONMENT%]
if /I "%ENVIRONMENT%"=="dev" (
    set "REQUIREMENTS_FILE=config\requirements-dev.txt"
    set "ENV_NAME=Development"
    set "ENV_DESC=Includes all dependencies plus development tools (testing, linting, debugging)"
) else if /I "%ENVIRONMENT%"=="prod" (
    set "REQUIREMENTS_FILE=config\requirements-prod.txt"
    set "ENV_NAME=Production"
    set "ENV_DESC=Minimal dependencies for production deployment"
) else if /I "%ENVIRONMENT%"=="standard" (
    set "REQUIREMENTS_FILE=config\requirements.txt"
    set "ENV_NAME=Standard"
    set "ENV_DESC=All features without development tools"
) else (
    echo ❌ Invalid environment: %ENVIRONMENT%
    echo Valid environments: dev, prod, standard
    exit /b 1
)

echo 🎯 Environment: %ENV_NAME%
echo 📝 Description: %ENV_DESC%
echo 📄 Requirements: %REQUIREMENTS_FILE%
echo.

:: Comprehensive Python validation
echo 🐍 Validating Python installation...
call :validate_python
if errorlevel 1 (
    echo ❌ SETUP FAILED: Python validation failed
    echo 📋 Please install Python 3.10+ from https://python.org
    call :show_setup_summary
    pause
    exit /b 1
)
echo ✅ Python validation passed
echo.

:: Robust virtual environment setup
echo 🐍 Setting up virtual environment...
call :setup_virtual_environment
if errorlevel 1 (
    echo ❌ SETUP FAILED: Virtual environment setup failed
    call :show_setup_summary
    pause
    exit /b 1
)
echo ✅ Virtual environment ready
echo.

:: Robust dependency installation
echo 📦 Installing dependencies with retry logic...
call :install_dependencies_robust
if errorlevel 1 (
    echo ❌ SETUP FAILED: Dependency installation failed
    call :show_setup_summary
    pause
    exit /b 1
)
echo ✅ All dependencies installed successfully
echo.

:: Robust environment file setup
echo ⚙️ Setting up configuration files...
call :setup_environment_file
if errorlevel 1 (
    echo ❌ SETUP FAILED: Environment file setup failed
    call :show_setup_summary
    pause
    exit /b 1
)
echo ✅ Configuration files ready
echo.

:: Robust database initialization
echo 🗄️ Initializing database and data structures...
call :initialize_database_robust
if errorlevel 1 (
    echo ❌ SETUP FAILED: Database initialization failed
    call :show_setup_summary
    pause
    exit /b 1
)
echo ✅ Database and data structures ready
echo.

:: Run security check if in development environment
if "%ENVIRONMENT%"=="dev" (
    echo 🔒 Running security check...
    python scripts\security_check.py --quiet
    if errorlevel 0 (
        echo ✅ Security check passed
    ) else (
        echo ⚠️ Security check found issues (see above)
    )
    echo.
)

:: Show environment-specific completion message
echo 🎉 Setup Complete (%ENV_NAME% Environment)!
echo ==========================================
echo.
echo ✅ Virtual environment: .venv\
echo ✅ Dependencies: %REQUIREMENTS_FILE%
echo ✅ Playwright browsers: Installed
echo ✅ Configuration: .env file created
echo ✅ Database: Initialized
echo ✅ Scripts: Ready
if "%ENVIRONMENT%"=="dev" echo ✅ Development tools: Available
echo.

:: Show next steps based on environment
if "%ENVIRONMENT%"=="dev" (
    echo 🚀 Development Environment Ready!
    echo ================================
    echo.
    echo 📚 Available development tools:
    echo    • pytest - Testing framework
    echo    • black - Code formatter
    echo    • mypy - Type checker
    echo    • pylint - Code linter
    echo    • pip-audit - Security scanner
    echo.
    echo 🔧 Development commands:
    echo    scripts\start_app.bat          # Start development server
    echo    pytest                          # Run tests
    echo    black src\                      # Format code
    echo    mypy src\                       # Type checking
    echo    pip-audit                       # Security audit
    echo.
) else if "%ENVIRONMENT%"=="prod" (
    echo 🚀 Production Environment Ready!
    echo ===============================
    echo.
    echo ⚠️ Production Notes:
    echo    • Minimal dependencies installed for security
    echo    • Development tools not available
    echo    • Configure proper secrets management
    echo    • Use production WSGI server (gunicorn)
    echo.
    echo 🔧 Production commands:
    echo    scripts\start_app.bat          # Start application
    echo    gunicorn app:app                # Production server
    echo.
) else (
    echo 🚀 Standard Environment Ready!
    echo =============================
    echo.
    echo 📝 Standard installation includes all features
    echo    without development tools
    echo.
    echo 🔧 Commands:
    echo    scripts\start_app.bat          # Start application
    echo.
)

echo 🔧 Configuration:
echo    Edit .env file to add your API keys
echo    See config\README-requirements.md for dependency info
echo.
echo 📖 Documentation:
echo    docs\README.md                  # Complete guide
echo    docs\PRODUCTION.md              # Production deployment
echo    config\README-requirements.md   # Requirements guide

exit /b 0

:: Function to find project root directory
:find_project_root
set "CURRENT_DIR=%CD%"

:: Check if we're already in project root (has app.py and src/ directory)
if exist "app.py" if exist "src\kitco_research_ai" (
    set "PROJECT_ROOT=%CD%"
    exit /b 0
)

:: Search upward for project root
set "SEARCH_DIR=%CURRENT_DIR%"
:search_loop
if "%SEARCH_DIR%"=="" goto search_failed
if exist "%SEARCH_DIR%\app.py" if exist "%SEARCH_DIR%\src\kitco_research_ai" (
    set "PROJECT_ROOT=%SEARCH_DIR%"
    exit /b 0
)
for %%I in ("%SEARCH_DIR%\.") do set "PARENT_DIR=%%~dpI"
set "PARENT_DIR=%PARENT_DIR:~0,-1%"
if "%PARENT_DIR%"=="%SEARCH_DIR%" goto search_failed
set "SEARCH_DIR=%PARENT_DIR%"
goto search_loop

:search_failed
echo ❌ Error: Could not find project root directory
echo    Please run this script from the kitco_research_ai project directory
exit /b 1

:: ============================================================================
:: ROBUST SETUP FUNCTIONS
:: ============================================================================

:validate_python
echo   🔍 Checking Python availability...
python --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ Python not found in PATH
    echo   📋 Install Python 3.10+ from https://python.org
    exit /b 1
)

echo   🔍 Checking Python version...
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo   📋 Found Python %PYTHON_VERSION%

python -c "import sys; sys.exit(0 if sys.version_info >= (3, 10) else 1)" 2>nul
if errorlevel 1 (
    echo   ❌ Python 3.10+ required, found %PYTHON_VERSION%
    exit /b 1
)

echo   🔍 Checking pip availability...
python -m pip --version >nul 2>&1
if errorlevel 1 (
    echo   ❌ pip not available
    exit /b 1
)

echo   ✅ Python %PYTHON_VERSION% with pip validated
exit /b 0

:setup_virtual_environment
echo   🔍 Checking for existing virtual environment...
if exist ".venv\Scripts\python.exe" (
    echo   ⚠️ Virtual environment exists, validating...
    call .venv\Scripts\activate.bat
    python -c "import sys; print('✅ Virtual environment valid')" 2>nul
    if errorlevel 1 (
        echo   ⚠️ Virtual environment corrupted, recreating...
        rmdir /s /q .venv 2>nul
        goto create_venv
    )
    echo   ✅ Existing virtual environment is valid
    goto upgrade_pip
)

:create_venv
echo   🔧 Creating new virtual environment...
python -m venv .venv
if errorlevel 1 (
    echo   ❌ Failed to create virtual environment
    exit /b 1
)

echo   🔄 Activating virtual environment...
call .venv\Scripts\activate.bat
if errorlevel 1 (
    echo   ❌ Failed to activate virtual environment
    exit /b 1
)

:upgrade_pip
echo   📦 Upgrading pip to latest version...
python -m pip install --upgrade pip --quiet
if errorlevel 1 (
    echo   ⚠️ Pip upgrade failed, continuing with existing version...
)

exit /b 0

:install_dependencies_robust
echo   🔍 Validating requirements file...
if not exist "%REQUIREMENTS_FILE%" (
    echo   ❌ Requirements file not found: %REQUIREMENTS_FILE%
    exit /b 1
)

echo   📦 Installing core dependencies first...
:: Install critical packages first to avoid conflicts
call .venv\Scripts\activate.bat
python -m pip install --upgrade pip setuptools wheel --quiet

echo   📦 Installing Flask and core web framework...
python -m pip install flask flask-socketio flask-wtf flask-cors --quiet
if errorlevel 1 (
    echo   ❌ Failed to install core web framework
    exit /b 1
)

echo   📦 Installing LangChain and AI dependencies...
python -m pip install langchain langchain-community langchain-core langchain-openai langchain-ollama --quiet
if errorlevel 1 (
    echo   ❌ Failed to install AI dependencies
    exit /b 1
)

echo   📦 Installing database and storage dependencies...
python -m pip install sqlalchemy alembic --quiet
if errorlevel 1 (
    echo   ❌ Failed to install database dependencies
    exit /b 1
)

echo   📦 Installing web scraping dependencies...
python -m pip install playwright beautifulsoup4 requests --quiet
if errorlevel 1 (
    echo   ❌ Failed to install web scraping dependencies
    exit /b 1
)

echo   📦 Installing remaining dependencies from requirements file...
set RETRY_COUNT=0
:install_retry
python -m pip install -r "%REQUIREMENTS_FILE%" --quiet
if errorlevel 1 (
    set /a RETRY_COUNT+=1
    if !RETRY_COUNT! LEQ 3 (
        echo   ⚠️ Installation failed, retrying ^(!RETRY_COUNT!/3^)...
        timeout /t 2 >nul
        goto install_retry
    ) else (
        echo   ❌ Failed to install dependencies after 3 attempts
        echo   📋 Check %SETUP_LOG% for details
        exit /b 1
    )
)

echo   🌐 Installing Playwright browsers...
python -m playwright install --quiet
if errorlevel 1 (
    echo   ⚠️ Playwright browser installation failed, continuing...
    echo   📋 You may need to run: python -m playwright install
)

exit /b 0

:setup_environment_file
echo   🔍 Setting up environment configuration...
if exist ".env" (
    echo   ⚠️ Environment file exists, backing up...
    copy .env .env.backup.%DATE:/=-%_%TIME::=-% >nul 2>&1
)

if exist "src\kitco_research_ai\defaults\.env.template" (
    echo   📋 Copying from template...
    copy "src\kitco_research_ai\defaults\.env.template" .env >nul 2>&1
) else if exist ".env.example" (
    echo   📋 Copying from example...
    copy ".env.example" .env >nul 2>&1
) else (
    echo   📋 Creating basic environment file...
    call :create_basic_env_file
)

if not exist ".env" (
    echo   ❌ Failed to create environment file
    exit /b 1
)

echo   ✅ Environment file ready
exit /b 0

:create_basic_env_file
(
echo # Kitco Research AI - Environment Configuration
echo SECRET_KEY=kitco_research_ai_secret_key_change_in_production
echo ENVIRONMENT=development
echo DEBUG=true
echo DATABASE_URL=sqlite:///data/ldr.db
echo HOST=0.0.0.0
echo PORT=8765
echo LOG_LEVEL=INFO
echo # Add your API keys below:
echo # OPENAI_API_KEY=your_openai_api_key_here
echo # SERPAPI_API_KEY=your_serpapi_key_here
) > .env
exit /b 0

:initialize_database_robust
echo   🗄️ Initializing database and data directory...
if not exist "data" (
    mkdir data
)

echo   🔧 Running database initialization...
python src\kitco_research_ai\setup_data_dir.py
if errorlevel 1 (
    echo   ❌ Database initialization failed
    exit /b 1
)

echo   🔍 Verifying database setup...
python -c "import sqlite3; conn=sqlite3.connect('data/ldr.db'); print('✅ Database accessible')" 2>nul
if errorlevel 1 (
    echo   ⚠️ Database verification failed, but continuing...
)

exit /b 0

:show_setup_summary
echo.
echo 📋 SETUP SUMMARY
echo ================
if "%SETUP_FAILED%"=="true" (
    echo ❌ Setup failed - see errors above
    echo 📝 Log file: %SETUP_LOG%
    echo.
    echo 🔧 Common solutions:
    echo    1. Install Python 3.10+ from https://python.org
    echo    2. Ensure internet connection for package downloads
    echo    3. Run as Administrator if permission issues
    echo    4. Check antivirus isn't blocking Python
) else (
    echo ✅ Setup completed successfully!
    echo 🌐 Access: http://localhost:8765
    echo 📝 Log file: %SETUP_LOG%
)
echo.
exit /b 0
