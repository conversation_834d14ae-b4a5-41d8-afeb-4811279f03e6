#!/usr/bin/env python3
"""
Kitco Research AI - Requirements Validation and Fixing Script
This script validates and fixes common dependency issues
"""

import os
import sys
import subprocess
import importlib
from pathlib import Path


def find_project_root():
    """Find the project root directory"""
    current_dir = Path.cwd()
    
    # Check current directory first
    if (current_dir / 'app.py').exists() and (current_dir / 'src' / 'kitco_research_ai').exists():
        return current_dir
    
    # Search upward
    for parent in current_dir.parents:
        if (parent / 'app.py').exists() and (parent / 'src' / 'kitco_research_ai').exists():
            return parent
    
    raise FileNotFoundError("Could not find project root directory")


def check_critical_packages():
    """Check and install critical packages that are commonly missing"""
    critical_packages = [
        'flask',
        'flask_socketio', 
        'flask_wtf',
        'flask_cors',
        'langchain',
        'langchain_community',
        'langchain_core', 
        'langchain_openai',
        'langchain_ollama',
        'sqlalchemy',
        'alembic',
        'playwright',
        'beautifulsoup4',
        'requests',
        'loguru',
        'python_dotenv',
        'pydantic'
    ]
    
    missing_packages = []
    
    print("🔍 Checking critical packages...")
    
    for package in critical_packages:
        try:
            # Handle package name variations
            import_name = package.replace('_', '-').replace('-', '_')
            if import_name == 'flask_socketio':
                import_name = 'flask_socketio'
            elif import_name == 'flask_wtf':
                import_name = 'flask_wtf'
            elif import_name == 'flask_cors':
                import_name = 'flask_cors'
            elif import_name == 'python_dotenv':
                import_name = 'dotenv'
            elif import_name == 'beautifulsoup4':
                import_name = 'bs4'
                
            importlib.import_module(import_name)
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package} - MISSING")
            missing_packages.append(package)
    
    return missing_packages


def install_missing_packages(missing_packages):
    """Install missing packages with proper error handling"""
    if not missing_packages:
        print("✅ All critical packages are installed")
        return True
    
    print(f"📦 Installing {len(missing_packages)} missing packages...")
    
    # Install packages one by one for better error handling
    failed_packages = []
    
    for package in missing_packages:
        print(f"  📦 Installing {package}...")
        try:
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package, '--quiet'
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package} installed successfully")
            else:
                print(f"  ❌ {package} installation failed: {result.stderr}")
                failed_packages.append(package)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} installation timed out")
            failed_packages.append(package)
        except Exception as e:
            print(f"  ❌ {package} installation error: {e}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"❌ Failed to install: {', '.join(failed_packages)}")
        return False
    
    print("✅ All missing packages installed successfully")
    return True


def validate_playwright_browsers():
    """Validate and install Playwright browsers if needed"""
    print("🌐 Checking Playwright browsers...")
    
    try:
        from playwright.sync_api import sync_playwright
        
        with sync_playwright() as p:
            try:
                browser = p.chromium.launch()
                browser.close()
                print("  ✅ Chromium browser available")
                return True
            except Exception:
                print("  ❌ Chromium browser not available")
                
        print("  📦 Installing Playwright browsers...")
        result = subprocess.run([
            sys.executable, '-m', 'playwright', 'install'
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("  ✅ Playwright browsers installed")
            return True
        else:
            print(f"  ❌ Playwright browser installation failed: {result.stderr}")
            return False
            
    except ImportError:
        print("  ❌ Playwright not installed")
        return False
    except Exception as e:
        print(f"  ❌ Playwright validation error: {e}")
        return False


def validate_database_setup():
    """Validate database setup and initialize if needed"""
    print("🗄️ Checking database setup...")
    
    project_root = find_project_root()
    data_dir = project_root / 'data'
    db_file = data_dir / 'ldr.db'
    
    if not data_dir.exists():
        print("  📁 Creating data directory...")
        data_dir.mkdir(exist_ok=True)
    
    if not db_file.exists():
        print("  🔧 Initializing database...")
        try:
            setup_script = project_root / 'src' / 'kitco_research_ai' / 'setup_data_dir.py'
            if setup_script.exists():
                result = subprocess.run([
                    sys.executable, str(setup_script)
                ], capture_output=True, text=True, cwd=project_root)
                
                if result.returncode == 0:
                    print("  ✅ Database initialized")
                else:
                    print(f"  ❌ Database initialization failed: {result.stderr}")
                    return False
            else:
                print("  ⚠️ Database setup script not found")
                return False
        except Exception as e:
            print(f"  ❌ Database initialization error: {e}")
            return False
    else:
        print("  ✅ Database exists")
    
    # Validate database accessibility
    try:
        import sqlite3
        conn = sqlite3.connect(str(db_file))
        conn.close()
        print("  ✅ Database accessible")
        return True
    except Exception as e:
        print(f"  ❌ Database validation error: {e}")
        return False


def validate_environment_file():
    """Validate and create environment file if needed"""
    print("⚙️ Checking environment configuration...")
    
    project_root = find_project_root()
    env_file = project_root / '.env'
    
    if env_file.exists():
        print("  ✅ Environment file exists")
        return True
    
    print("  📋 Creating environment file...")
    
    # Try to find template
    template_file = project_root / 'src' / 'kitco_research_ai' / 'defaults' / '.env.template'
    example_file = project_root / '.env.example'
    
    if template_file.exists():
        print("  📋 Copying from template...")
        with open(template_file, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
    elif example_file.exists():
        print("  📋 Copying from example...")
        with open(example_file, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
    else:
        print("  📋 Creating basic environment file...")
        env_content = """# Kitco Research AI - Environment Configuration
SECRET_KEY=kitco_research_ai_secret_key_change_in_production
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=sqlite:///data/ldr.db
HOST=0.0.0.0
PORT=8765
LOG_LEVEL=INFO

# Add your API keys below:
# OPENAI_API_KEY=your_openai_api_key_here
# SERPAPI_API_KEY=your_serpapi_key_here
"""
        with open(env_file, 'w') as f:
            f.write(env_content)
    
    print("  ✅ Environment file created")
    return True


def main():
    """Main validation and fixing routine"""
    print("🔬 Kitco Research AI - Requirements Validation")
    print("=" * 50)
    
    try:
        # Change to project root
        project_root = find_project_root()
        os.chdir(project_root)
        print(f"📁 Project root: {project_root}")
        print()
        
        # Check critical packages
        missing_packages = check_critical_packages()
        
        # Install missing packages
        if not install_missing_packages(missing_packages):
            print("❌ Failed to install some packages")
            return 1
        
        print()
        
        # Validate Playwright browsers
        if not validate_playwright_browsers():
            print("⚠️ Playwright browser validation failed")
        
        print()
        
        # Validate environment file
        if not validate_environment_file():
            print("❌ Environment file validation failed")
            return 1
        
        print()
        
        # Validate database setup
        if not validate_database_setup():
            print("❌ Database validation failed")
            return 1
        
        print()
        print("🎉 All validations passed!")
        print("✅ Your environment is ready for Kitco Research AI")
        
        return 0
        
    except Exception as e:
        print(f"❌ Validation failed: {e}")
        return 1


if __name__ == '__main__':
    sys.exit(main())
