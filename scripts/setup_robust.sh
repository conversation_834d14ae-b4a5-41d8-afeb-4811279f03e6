#!/bin/bash
# Kitco Research AI - Robust Initial Setup Script for Unix/Linux/macOS
# This script performs bulletproof initial setup with comprehensive error handling

# Enhanced error handling
set -euo pipefail  # Exit on any error, undefined variables, pipe failures

# Global variables
SETUP_FAILED=false
SETUP_LOG="/tmp/kitco_setup_$(date +%Y%m%d_%H%M%S).log"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo ""
echo "🚀 Kitco Research AI - Robust Setup System"
echo "=========================================="
echo "🎯 Ensuring bulletproof setup for all developers"
echo ""

# Initialize log
echo "Setup started at $(date)" > "$SETUP_LOG"
echo "📝 Setup Log: $SETUP_LOG"

# Error handler
handle_error() {
    local exit_code=$1
    local line_number=$2
    echo -e "${RED}❌ SETUP FAILED${NC}"
    echo "Error occurred in setup script at line $line_number (exit code: $exit_code)"
    echo "Check log file: $SETUP_LOG"
    SETUP_FAILED=true
    show_setup_summary
    exit $exit_code
}

# Set error trap
trap 'handle_error $? $LINENO' ERR

# Function to find project root directory
find_project_root() {
    local current_dir="$(pwd)"
    local script_dir="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

    # Check if we're already in project root
    if [ -f "app.py" ] && [ -d "src/kitco_research_ai" ]; then
        echo "$(pwd)"
        return 0
    fi

    # Check if script is in scripts/ subdirectory
    local parent_dir="$(dirname "$script_dir")"
    if [ -f "$parent_dir/app.py" ] && [ -d "$parent_dir/src/kitco_research_ai" ]; then
        echo "$parent_dir"
        return 0
    fi

    # Search upward for project root
    local search_dir="$current_dir"
    while [ "$search_dir" != "/" ]; do
        if [ -f "$search_dir/app.py" ] && [ -d "$search_dir/src/kitco_research_ai" ]; then
            echo "$search_dir"
            return 0
        fi
        search_dir="$(dirname "$search_dir")"
    done

    echo -e "${RED}❌ Error: Could not find project root directory${NC}" >&2
    echo "   Please run this script from the kitco_research_ai project directory" >&2
    exit 1
}

# Validate Python installation
validate_python() {
    echo "🐍 Validating Python installation..."
    
    # Check if python3 is available
    if ! command -v python3 &> /dev/null; then
        echo -e "${RED}❌ python3 not found${NC}"
        echo "📋 Please install Python 3.10+ from your package manager or https://python.org"
        return 1
    fi
    
    # Check Python version
    local python_version=$(python3 --version 2>&1 | cut -d' ' -f2)
    echo "📋 Found Python $python_version"
    
    if ! python3 -c "import sys; exit(0 if sys.version_info >= (3, 10) else 1)" 2>/dev/null; then
        echo -e "${RED}❌ Python 3.10+ required, found $python_version${NC}"
        return 1
    fi
    
    # Check pip availability
    if ! python3 -m pip --version &> /dev/null; then
        echo -e "${RED}❌ pip not available${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Python $python_version with pip validated${NC}"
    return 0
}

# Setup virtual environment robustly
setup_virtual_environment() {
    echo "🐍 Setting up virtual environment..."
    
    # Check for existing virtual environment
    if [ -f ".venv/bin/python" ]; then
        echo "⚠️ Virtual environment exists, validating..."
        if source .venv/bin/activate && python -c "import sys; print('✅ Virtual environment valid')" 2>/dev/null; then
            echo -e "${GREEN}✅ Existing virtual environment is valid${NC}"
        else
            echo "⚠️ Virtual environment corrupted, recreating..."
            rm -rf .venv
        fi
    fi
    
    # Create virtual environment if needed
    if [ ! -f ".venv/bin/python" ]; then
        echo "🔧 Creating new virtual environment..."
        python3 -m venv .venv
    fi
    
    # Activate virtual environment
    echo "🔄 Activating virtual environment..."
    source .venv/bin/activate
    
    # Upgrade pip
    echo "📦 Upgrading pip to latest version..."
    python -m pip install --upgrade pip --quiet || echo "⚠️ Pip upgrade failed, continuing..."
    
    return 0
}

# Install dependencies with retry logic
install_dependencies_robust() {
    echo "📦 Installing dependencies with retry logic..."
    
    # Validate requirements file
    if [ ! -f "$REQUIREMENTS_FILE" ]; then
        echo -e "${RED}❌ Requirements file not found: $REQUIREMENTS_FILE${NC}"
        return 1
    fi
    
    # Activate virtual environment
    source .venv/bin/activate
    
    # Install critical packages first
    echo "📦 Installing core dependencies first..."
    local critical_packages=(
        "flask"
        "flask-socketio" 
        "flask-wtf"
        "flask-cors"
        "langchain"
        "langchain-community"
        "langchain-core"
        "langchain-openai"
        "langchain-ollama"
        "sqlalchemy"
        "alembic"
        "playwright"
        "beautifulsoup4"
        "requests"
        "loguru"
        "python-dotenv"
        "pydantic"
    )
    
    for package in "${critical_packages[@]}"; do
        echo "  📦 Installing $package..."
        if ! python -m pip install "$package" --quiet; then
            echo -e "${YELLOW}⚠️ Failed to install $package, will retry with requirements file${NC}"
        fi
    done
    
    # Install from requirements file with retry
    echo "📦 Installing remaining dependencies from requirements file..."
    local retry_count=0
    local max_retries=3
    
    while [ $retry_count -lt $max_retries ]; do
        if python -m pip install -r "$REQUIREMENTS_FILE" --quiet; then
            echo -e "${GREEN}✅ All dependencies installed successfully${NC}"
            break
        else
            retry_count=$((retry_count + 1))
            if [ $retry_count -lt $max_retries ]; then
                echo "⚠️ Installation failed, retrying ($retry_count/$max_retries)..."
                sleep 2
            else
                echo -e "${RED}❌ Failed to install dependencies after $max_retries attempts${NC}"
                return 1
            fi
        fi
    done
    
    # Install Playwright browsers
    echo "🌐 Installing Playwright browsers..."
    if ! python -m playwright install --quiet; then
        echo -e "${YELLOW}⚠️ Playwright browser installation failed, continuing...${NC}"
        echo "📋 You may need to run: python -m playwright install"
    fi
    
    return 0
}

# Setup environment file
setup_environment_file() {
    echo "⚙️ Setting up environment configuration..."
    
    if [ -f ".env" ]; then
        echo "⚠️ Environment file exists, backing up..."
        cp .env ".env.backup.$(date +%Y%m%d_%H%M%S)" 2>/dev/null || true
    fi
    
    if [ -f "src/kitco_research_ai/defaults/.env.template" ]; then
        echo "📋 Copying from template..."
        cp "src/kitco_research_ai/defaults/.env.template" .env
    elif [ -f ".env.example" ]; then
        echo "📋 Copying from example..."
        cp ".env.example" .env
    else
        echo "📋 Creating basic environment file..."
        cat > .env << 'EOF'
# Kitco Research AI - Environment Configuration
SECRET_KEY=kitco_research_ai_secret_key_change_in_production
ENVIRONMENT=development
DEBUG=true
DATABASE_URL=sqlite:///data/ldr.db
HOST=0.0.0.0
PORT=8765
LOG_LEVEL=INFO

# Add your API keys below:
# OPENAI_API_KEY=your_openai_api_key_here
# SERPAPI_API_KEY=your_serpapi_key_here
EOF
    fi
    
    if [ ! -f ".env" ]; then
        echo -e "${RED}❌ Failed to create environment file${NC}"
        return 1
    fi
    
    echo -e "${GREEN}✅ Environment file ready${NC}"
    return 0
}

# Initialize database robustly
initialize_database_robust() {
    echo "🗄️ Initializing database and data directory..."
    
    # Create data directory
    mkdir -p data
    
    # Activate virtual environment
    source .venv/bin/activate
    
    # Run database initialization
    echo "🔧 Running database initialization..."
    if ! python src/kitco_research_ai/setup_data_dir.py; then
        echo -e "${RED}❌ Database initialization failed${NC}"
        return 1
    fi
    
    # Verify database setup
    echo "🔍 Verifying database setup..."
    if ! python -c "import sqlite3; conn=sqlite3.connect('data/ldr.db'); print('✅ Database accessible')" 2>/dev/null; then
        echo -e "${YELLOW}⚠️ Database verification failed, but continuing...${NC}"
    fi
    
    return 0
}

# Show setup summary
show_setup_summary() {
    echo ""
    echo "📋 SETUP SUMMARY"
    echo "================"
    if [ "$SETUP_FAILED" = true ]; then
        echo -e "${RED}❌ Setup failed - see errors above${NC}"
        echo "📝 Log file: $SETUP_LOG"
        echo ""
        echo "🔧 Common solutions:"
        echo "   1. Install Python 3.10+ from your package manager"
        echo "   2. Ensure internet connection for package downloads"
        echo "   3. Check disk space and permissions"
        echo "   4. Try running with sudo if permission issues"
    else
        echo -e "${GREEN}✅ Setup completed successfully!${NC}"
        echo "🌐 Access: http://localhost:8765"
        echo "📝 Log file: $SETUP_LOG"
    fi
    echo ""
}

# Main setup function
main() {
    # Change to project root directory
    PROJECT_ROOT="$(find_project_root)"
    cd "$PROJECT_ROOT"
    
    echo "📁 Project Root: $PROJECT_ROOT"
    echo ""
    
    # Parse command line arguments (simplified for robustness)
    ENVIRONMENT="dev"
    case "${1:-}" in
        --production) ENVIRONMENT="prod" ;;
        --development) ENVIRONMENT="dev" ;;
        --env) ENVIRONMENT="${2:-dev}" ;;
    esac
    
    # Determine requirements file
    case $ENVIRONMENT in
        "dev")
            REQUIREMENTS_FILE="config/requirements-dev.txt"
            ENV_NAME="Development"
            ;;
        "prod")
            REQUIREMENTS_FILE="config/requirements-prod.txt"
            ENV_NAME="Production"
            ;;
        *)
            REQUIREMENTS_FILE="config/requirements.txt"
            ENV_NAME="Standard"
            ;;
    esac
    
    echo "🎯 Environment: $ENV_NAME"
    echo "📄 Requirements: $REQUIREMENTS_FILE"
    echo ""
    
    # Run validation and setup steps
    validate_python
    setup_virtual_environment
    install_dependencies_robust
    setup_environment_file
    initialize_database_robust
    
    # Make scripts executable
    echo "🔧 Setting up scripts..."
    chmod +x scripts/*.sh 2>/dev/null || true
    
    # Run validation script
    echo "🔬 Running final validation..."
    source .venv/bin/activate
    python scripts/validate_requirements.py
    
    echo ""
    echo -e "${GREEN}🎉 Setup Complete ($ENV_NAME Environment)!${NC}"
    echo "=========================================="
    echo ""
    echo "✅ Virtual environment: .venv/"
    echo "✅ Dependencies: $REQUIREMENTS_FILE"
    echo "✅ Configuration: .env file created"
    echo "✅ Database: Initialized"
    echo "✅ Scripts: Ready"
    echo ""
    echo "🚀 Next steps:"
    echo "   ./scripts/start_app.sh    # Start application"
    echo "   ./scripts/restart.sh      # Fresh restart"
    echo ""
    
    show_setup_summary
}

# Run main function
main "$@"
