#!/bin/bash
# Kitco Research AI - Unix Setup Convenience Script
# This script provides easy access to the setup functionality

# Check if we're in the right directory
if [ ! -f "app.py" ] || [ ! -d "src/kitco_research_ai" ]; then
    echo "❌ Error: This script must be run from the project root directory"
    echo "   Please navigate to the kitco_research_ai directory first"
    exit 1
fi

# Make sure the script is executable
chmod +x scripts/setup/setup.sh

# Run the Unix setup script (now in organized location)
./scripts/setup/setup.sh "$@"
